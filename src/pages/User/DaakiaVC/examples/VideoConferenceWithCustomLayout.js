import React, { useState, useCallback, useMemo } from 'react';
import { useTracks, usePinnedTracks, useCreateLayoutContext } from '@livekit/components-react';
import { Track } from 'livekit-client';
import { CustomLayoutManager, useCustomLayout, calculateOptimalLayout } from '../customFabs/CustomLayoutManager';

/**
 * Example of how to integrate the CustomLayoutManager into your VideoConference component
 */
export const VideoConferenceWithCustomLayout = ({ 
  room, 
  showEmojiReaction,
  setShowEmojiReaction,
  showRaiseHand,
  remoteRaisedHands,
  remoteEmojiReactions,
  setRemoteEmojiReactions,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  brightness,
  participantBrightness,
  participantColors,
  setParticipantColors,
  isWhiteboardOpen,
  ...props 
}) => {
  // Layout management
  const {
    viewMode,
    maxVisibleTiles,
    customConfig,
    setViewMode,
    setMaxVisibleTiles,
    updateLayout,
    createCustomConfig,
  } = useCustomLayout('grid');

  // Additional layout state
  const [showLayoutControls, setShowLayoutControls] = useState(true);
  const [autoLayoutEnabled, setAutoLayoutEnabled] = useState(true);

  // Get tracks and layout context
  const tracks = useTracks(
    [
      { source: Track.Source.Camera, withPlaceholder: true },
      { source: Track.Source.ScreenShare, withPlaceholder: false },
    ],
    { onlySubscribed: false }
  );

  const layoutContext = useCreateLayoutContext();
  const focusTrack = usePinnedTracks(layoutContext)?.[0];

  // Filter tracks based on current view
  const filteredTracks = useMemo(() => {
    let result = [...tracks];
    
    // Remove focus track from regular tracks if in focus mode
    if (focusTrack && viewMode === 'focus') {
      result = tracks.filter(track => track !== focusTrack);
    }
    
    // Filter screen share tracks if needed
    if (viewMode === 'carousel') {
      result = result.filter(track => track.source !== Track.Source.ScreenShare);
    }
    
    return result;
  }, [tracks, focusTrack, viewMode]);

  // Auto layout calculation
  const optimalViewMode = useMemo(() => {
    if (!autoLayoutEnabled) return viewMode;
    return calculateOptimalLayout(filteredTracks.length, window.innerWidth, window.innerHeight);
  }, [filteredTracks.length, autoLayoutEnabled, viewMode]);

  // Handle layout changes
  const handleLayoutChange = useCallback((newViewMode, config) => {
    console.log('Layout changed to:', newViewMode, config);
    // You can add analytics or other side effects here
  }, []);

  // Handle tile clicks
  const handleTileClick = useCallback((trackRef, participant) => {
    console.log('Tile clicked:', participant.identity);
    // You can implement focus/pin logic here
    if (layoutContext?.pin?.dispatch) {
      layoutContext.pin.dispatch({ msg: 'set_pin', trackReference: trackRef });
    }
  }, [layoutContext]);

  // Custom layout configurations for different scenarios
  const customConfigs = useMemo(() => ({
    presentation: createCustomConfig({
      maxTilesPerRow: 6,
      maxVisibleTiles: 6,
      tileAspectRatio: '4/3',
      showMetadata: false,
      avatarSize: 'small',
      className: 'presentation-mode'
    }),
    meeting: createCustomConfig({
      maxTilesPerRow: 3,
      maxVisibleTiles: 9,
      tileAspectRatio: '16/9',
      showMetadata: true,
      avatarSize: 'medium',
      className: 'meeting-mode'
    }),
    webinar: createCustomConfig({
      maxTilesPerRow: 8,
      maxVisibleTiles: 8,
      tileAspectRatio: '1/1',
      showMetadata: false,
      avatarSize: 'small',
      className: 'webinar-mode'
    })
  }), [createCustomConfig]);

  // Handle preset selection
  const handlePresetChange = useCallback((presetName) => {
    const config = customConfigs[presetName];
    if (config) {
      updateLayout('grid', config);
    }
  }, [customConfigs, updateLayout]);

  // Render different layouts based on state
  if (isWhiteboardOpen) {
    // Whiteboard mode - show minimal participant tiles
    return (
      <div className="video-conference-whiteboard">
        <div className="whiteboard-participants">
          <CustomLayoutManager
            tracks={filteredTracks.slice(0, 4)} // Limit to 4 tiles in whiteboard mode
            viewMode="carousel"
            maxVisibleTiles={4}
            showMetadata={false}
            showConnectionQuality={false}
            enableHoverEffects={false}
            customLayoutConfig={customConfigs.webinar}
            onTileClick={handleTileClick}
            onLayoutChange={handleLayoutChange}
            showEmojiReaction={showEmojiReaction}
            setShowEmojiReaction={setShowEmojiReaction}
            showRaiseHand={showRaiseHand}
            remoteRaisedHands={remoteRaisedHands}
            remoteEmojiReactions={remoteEmojiReactions}
            setRemoteEmojiReactions={setRemoteEmojiReactions}
            isSelfVideoMirrored={isSelfVideoMirrored}
            setIsSelfVideoMirrored={setIsSelfVideoMirrored}
            brightness={brightness}
            participantBrightness={participantBrightness}
            participantColors={participantColors}
            setParticipantColors={setParticipantColors}
            {...props}
          />
        </div>
        {/* Whiteboard component would go here */}
      </div>
    );
  }

  return (
    <div className="video-conference-custom">
      {/* Custom Layout Controls */}
      {showLayoutControls && (
        <div className="custom-video-controls">
          <div className="layout-presets">
            <button onClick={() => handlePresetChange('meeting')}>
              Meeting Mode
            </button>
            <button onClick={() => handlePresetChange('presentation')}>
              Presentation Mode
            </button>
            <button onClick={() => handlePresetChange('webinar')}>
              Webinar Mode
            </button>
          </div>
          
          <div className="layout-options">
            <label>
              <input
                type="checkbox"
                checked={autoLayoutEnabled}
                onChange={(e) => setAutoLayoutEnabled(e.target.checked)}
              />
              Auto Layout
            </label>
            
            <label>
              Max Tiles:
              <select
                value={maxVisibleTiles}
                onChange={(e) => setMaxVisibleTiles(parseInt(e.target.value))}
              >
                <option value={4}>4</option>
                <option value={9}>9</option>
                <option value={16}>16</option>
                <option value={25}>25</option>
              </select>
            </label>
          </div>
          
          <button 
            className="toggle-controls"
            onClick={() => setShowLayoutControls(false)}
          >
            Hide Controls
          </button>
        </div>
      )}

      {!showLayoutControls && (
        <button 
          className="show-controls"
          onClick={() => setShowLayoutControls(true)}
        >
          Show Layout Controls
        </button>
      )}

      {/* Main Layout */}
      <div className="main-video-area">
        <CustomLayoutManager
          tracks={filteredTracks}
          focusTrack={focusTrack}
          viewMode={autoLayoutEnabled ? optimalViewMode : viewMode}
          maxVisibleTiles={maxVisibleTiles}
          customLayoutConfig={customConfig}
          onTileClick={handleTileClick}
          onLayoutChange={handleLayoutChange}
          showEmojiReaction={showEmojiReaction}
          setShowEmojiReaction={setShowEmojiReaction}
          showRaiseHand={showRaiseHand}
          remoteRaisedHands={remoteRaisedHands}
          remoteEmojiReactions={remoteEmojiReactions}
          setRemoteEmojiReactions={setRemoteEmojiReactions}
          isSelfVideoMirrored={isSelfVideoMirrored}
          setIsSelfVideoMirrored={setIsSelfVideoMirrored}
          brightness={brightness}
          participantBrightness={participantBrightness}
          participantColors={participantColors}
          setParticipantColors={setParticipantColors}
          {...props}
        />
      </div>

      {/* Layout Info */}
      <div className="layout-info">
        <span>
          Current: {autoLayoutEnabled ? optimalViewMode : viewMode} | 
          Showing: {Math.min(filteredTracks.length, maxVisibleTiles)} / {filteredTracks.length} participants
        </span>
      </div>
    </div>
  );
};

// CSS for the example (you can add this to your SCSS file)
const exampleStyles = `
.video-conference-custom {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.custom-video-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  gap: 16px;
}

.layout-presets {
  display: flex;
  gap: 8px;
}

.layout-presets button {
  padding: 6px 12px;
  border: 1px solid #4CAF50;
  background: transparent;
  color: #4CAF50;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.layout-presets button:hover {
  background: #4CAF50;
  color: white;
}

.layout-options {
  display: flex;
  gap: 16px;
  align-items: center;
}

.layout-options label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: white;
}

.main-video-area {
  flex: 1;
  min-height: 0;
}

.layout-info {
  padding: 4px 16px;
  background: rgba(0, 0, 0, 0.05);
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
}

.video-conference-whiteboard {
  width: 100%;
  height: 100%;
  display: flex;
}

.whiteboard-participants {
  width: 200px;
  background: rgba(0, 0, 0, 0.1);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.show-controls,
.toggle-controls {
  padding: 4px 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: transparent;
  color: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
}
`;

export default VideoConferenceWithCustomLayout;
