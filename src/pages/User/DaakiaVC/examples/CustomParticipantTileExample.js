import React, { useState, useCallback, useEffect } from "react";
import { CustomParticipantTile } from "../customFabs/CustomParticipantTile";
import { useMapState } from "../hooks/useParticipantTileMemory";

/**
 * Example component showing how to use the CustomParticipantTile
 * with proper memory management and state handling
 */
export const CustomParticipantTileExample = ({ trackRef }) => {
  // State management with proper memory optimization
  const [showEmojiReaction, setShowEmojiReaction] = useState(null);
  const [showRaiseHand, setShowRaiseHand] = useState(false);
  const [isSelfVideoMirrored, setIsSelfVideoMirrored] = useState(true);
  const [brightness, setBrightness] = useState(100);

  // Use custom hook for Map state management
  const [
    remoteRaisedHands,
    setRemoteRaisedHands,
    updateRaisedHand,
    deleteRaisedHand,
  ] = useMapState();

  const [
    remoteEmojiReactions,
    setRemoteEmojiReactions,
    updateEmojiReaction,
    deleteEmojiReaction,
  ] = useMapState();

  const [
    participantBrightness,
    setParticipantBrightness,
    updateParticipantBrightness,
  ] = useMapState();

  const [
    participantColors,
    setParticipantColors,
    updateParticipantColor,
  ] = useMapState();

  // Optimized event handlers
  const handleParticipantClick = useCallback((participant) => {
    console.log("Participant clicked:", participant.identity);
  }, []);

  const handleEmojiReaction = useCallback((emoji) => {
    setShowEmojiReaction(emoji);
  }, []);

  const handleRaiseHand = useCallback(() => {
    setShowRaiseHand(prev => !prev);
  }, []);

  const handleBrightnessChange = useCallback((newBrightness) => {
    setBrightness(newBrightness);
  }, []);

  // Example of adding remote reactions (would typically come from LiveKit events)
  const addRemoteReaction = useCallback((participantId, reaction) => {
    updateEmojiReaction(participantId, reaction);
  }, [updateEmojiReaction]);

  const addRemoteRaiseHand = useCallback((participantId, isRaised) => {
    if (isRaised) {
      updateRaisedHand(participantId, true);
    } else {
      deleteRaisedHand(participantId);
    }
  }, [updateRaisedHand, deleteRaisedHand]);

  return (
    <div className="custom-participant-tile-container">
      <CustomParticipantTile
        trackRef={trackRef}
        showEmojiReaction={showEmojiReaction}
        setShowEmojiReaction={setShowEmojiReaction}
        showRaiseHand={showRaiseHand}
        remoteRaisedHands={remoteRaisedHands}
        remoteEmojiReactions={remoteEmojiReactions}
        setRemoteEmojiReactions={setRemoteEmojiReactions}
        onParticipantClick={handleParticipantClick}
        isSelfVideoMirrored={isSelfVideoMirrored}
        setIsSelfVideoMirrored={setIsSelfVideoMirrored}
        brightness={brightness}
        participantBrightness={participantBrightness}
        participantColors={participantColors}
        setParticipantColors={setParticipantColors}
        disableSpeakingIndicator={false}
        focusTrack={false}
      />
      
      {/* Example controls */}
      <div className="participant-controls">
        <button onClick={() => handleEmojiReaction("👍")}>
          👍
        </button>
        <button onClick={() => handleEmojiReaction("❤️")}>
          ❤️
        </button>
        <button onClick={() => handleEmojiReaction("😂")}>
          😂
        </button>
        <button onClick={handleRaiseHand}>
          {showRaiseHand ? "Lower Hand" : "Raise Hand"}
        </button>
        <button onClick={() => setIsSelfVideoMirrored(prev => !prev)}>
          {isSelfVideoMirrored ? "Unmirror" : "Mirror"} Video
        </button>
        <input
          type="range"
          min="50"
          max="150"
          value={brightness}
          onChange={(e) => handleBrightnessChange(parseInt(e.target.value))}
          title="Brightness"
        />
      </div>
    </div>
  );
};

/**
 * Example of how to integrate with LiveKit room events
 */
export const useCustomParticipantTileEvents = (room) => {
  const [remoteReactions, setRemoteReactions] = useMapState();
  const [remoteRaisedHands, setRemoteRaisedHands] = useMapState();

  useEffect(() => {
    if (!room) return;

    // Example event handlers for LiveKit room events
    const handleDataReceived = (payload, participant) => {
      try {
        const data = JSON.parse(new TextDecoder().decode(payload));
        
        switch (data.type) {
          case 'emoji_reaction':
            setRemoteReactions(prev => {
              const newMap = new Map(prev);
              newMap.set(participant.identity, data.emoji);
              return newMap;
            });
            break;
            
          case 'raise_hand':
            setRemoteRaisedHands(prev => {
              const newMap = new Map(prev);
              if (data.raised) {
                newMap.set(participant.identity, true);
              } else {
                newMap.delete(participant.identity);
              }
              return newMap;
            });
            break;
        }
      } catch (error) {
        console.error('Error parsing data:', error);
      }
    };

    const handleParticipantDisconnected = (participant) => {
      // Clean up participant data when they disconnect
      setRemoteReactions(prev => {
        const newMap = new Map(prev);
        newMap.delete(participant.identity);
        return newMap;
      });
      
      setRemoteRaisedHands(prev => {
        const newMap = new Map(prev);
        newMap.delete(participant.identity);
        return newMap;
      });
    };

    // Register event listeners
    room.on('dataReceived', handleDataReceived);
    room.on('participantDisconnected', handleParticipantDisconnected);

    // Cleanup
    return () => {
      room.off('dataReceived', handleDataReceived);
      room.off('participantDisconnected', handleParticipantDisconnected);
    };
  }, [room, setRemoteReactions, setRemoteRaisedHands]);

  return {
    remoteReactions,
    remoteRaisedHands,
    setRemoteReactions,
    setRemoteRaisedHands,
  };
};
