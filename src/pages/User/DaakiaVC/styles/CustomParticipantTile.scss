// Import your variables and mixins
@import 'variables';
@import 'mixins';

// Base participant tile styles
.custom-participant-tile {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  background: #1a1a1a;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  
  // Media container
  .custom-media-container {
    position: relative;
    width: 100%;
    height: 100%;
    background: #000;
    
    .custom-video-track {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  // Avatar container
  .custom-avatar-container {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    
    .custom-avatar {
      border: 3px solid rgba(255, 255, 255, 0.2);
      font-weight: 600;
      
      &.size-small {
        width: 40px !important;
        height: 40px !important;
        font-size: 14px;
      }
      
      &.size-medium {
        width: 60px !important;
        height: 60px !important;
        font-size: 18px;
      }
      
      &.size-large {
        width: 80px !important;
        height: 80px !important;
        font-size: 24px;
      }
    }
  }

  // Metadata container
  .custom-metadata-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    padding: 8px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 3;
    
    .custom-metadata-content {
      display: flex;
      align-items: center;
      gap: 6px;
      flex: 1;
      
      .custom-participant-name {
        color: white;
        font-size: 12px;
        font-weight: 500;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 120px;
      }
      
      .custom-mute-indicator {
        color: #ff4444;
        font-size: 14px;
      }
      
      .custom-encryption-icon {
        color: #4CAF50;
        font-size: 12px;
      }
      
      .custom-screen-share-icon {
        color: #2196F3;
        font-size: 14px;
      }
    }
    
    .custom-connection-quality {
      font-size: 12px;
    }
  }

  // Overlays
  .custom-reaction-overlay,
  .custom-raise-hand-overlay {
    position: absolute;
    top: 12px;
    right: 12px;
    z-index: 4;
    
    &.local {
      top: 12px;
      left: 12px;
      right: auto;
    }
  }

  .custom-screen-share-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
    z-index: 3;
  }

  .custom-focus-toggle {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 5;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  // Hover effects
  &.hover-effects-enabled:hover {
    border-color: #4CAF50;
    transform: scale(1.02);
    
    .custom-focus-toggle {
      opacity: 1;
    }
  }

  // Speaking indicator
  &.is-speaking {
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.3);
  }

  // Local participant
  &.is-local {
    .custom-media-container .custom-video-track {
      transform: scaleX(-1); // Mirror local video
    }
  }

  // Screen share
  &.is-screen-share {
    .custom-avatar-container {
      display: none;
    }
  }

  // Focus track
  &.is-focus-track {
    border-color: #FF9800;
    
    .custom-metadata-container .custom-participant-name {
      font-size: 14px;
      font-weight: 600;
    }
    
    .custom-avatar-container .custom-avatar {
      border-color: #FF9800;
    }
  }
}

// Layout-specific styles
.custom-grid-layout {
  aspect-ratio: 16/9;
  
  // Responsive grid sizing
  @media (max-width: 768px) {
    aspect-ratio: 4/3;
  }
  
  @media (max-width: 480px) {
    aspect-ratio: 1/1;
  }
}

.custom-focus-layout {
  aspect-ratio: 4/3;
  max-width: 200px;
  
  .custom-metadata-container {
    padding: 4px 8px;
    
    .custom-participant-name {
      font-size: 10px;
      max-width: 80px;
    }
  }
}

.custom-carousel-layout {
  aspect-ratio: 1/1;
  max-width: 120px;
  
  .custom-metadata-container {
    display: none;
  }
  
  .custom-avatar-container .custom-avatar {
    width: 40px !important;
    height: 40px !important;
    font-size: 14px;
  }
}

.custom-speaker-layout {
  aspect-ratio: 16/9;
  width: 100%;
  max-width: none;
  
  .custom-metadata-container .custom-participant-name {
    font-size: 16px;
    font-weight: 600;
    max-width: 200px;
  }
  
  .custom-avatar-container .custom-avatar {
    width: 100px !important;
    height: 100px !important;
    font-size: 28px;
  }
}

.custom-mobile-layout {
  aspect-ratio: 9/16;
  
  .custom-metadata-container {
    padding: 6px 10px;
    
    .custom-participant-name {
      font-size: 11px;
      max-width: 100px;
    }
  }
}

// View mode specific styles
.view-mode-grid {
  // Grid view specific styles
  .custom-participant-tile {
    margin: 4px;
  }
}

.view-mode-focus {
  // Focus view specific styles
  .custom-participant-tile {
    margin: 2px;
  }
}

.view-mode-carousel {
  // Carousel view specific styles
  .custom-participant-tile {
    margin: 1px;
  }
}

.view-mode-speaker {
  // Speaker view specific styles
  .custom-participant-tile {
    margin: 0;
  }
}

// Container layouts for different tile counts
.custom-tiles-container {
  display: grid;
  gap: 8px;
  padding: 8px;
  width: 100%;
  height: 100%;
  
  // 1 tile (speaker view)
  &.tiles-1 {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;
  }
  
  // 2 tiles
  &.tiles-2 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      grid-template-rows: 1fr 1fr;
    }
  }
  
  // 3-4 tiles
  &.tiles-3,
  &.tiles-4 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
  }
  
  // 5-6 tiles
  &.tiles-5,
  &.tiles-6 {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    
    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(3, 1fr);
    }
  }
  
  // 7-9 tiles
  &.tiles-7,
  &.tiles-8,
  &.tiles-9 {
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
  }
  
  // 10-16 tiles
  &.tiles-10,
  &.tiles-11,
  &.tiles-12,
  &.tiles-13,
  &.tiles-14,
  &.tiles-15,
  &.tiles-16 {
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(4, 1fr);
    
    @media (max-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(auto-fit, 1fr);
    }
    
    @media (max-width: 480px) {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(auto-fit, 1fr);
    }
  }
  
  // More than 16 tiles (scrollable)
  &.tiles-many {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    grid-auto-rows: 150px;
    overflow-y: auto;
    max-height: 100%;
  }
}

// Animations
@keyframes tileAppear {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.custom-participant-tile {
  animation: tileAppear 0.3s ease-out;
}

// Dark theme adjustments
.dark-theme {
  .custom-participant-tile {
    background: #2a2a2a;
    border-color: #444;
    
    &.is-speaking {
      border-color: #4CAF50;
    }
    
    &.hover-effects-enabled:hover {
      border-color: #4CAF50;
    }
  }
}

// High contrast mode
.high-contrast {
  .custom-participant-tile {
    border-width: 3px;

    .custom-metadata-container {
      background: rgba(0, 0, 0, 0.9);
    }

    .custom-participant-name {
      font-weight: 700;
    }
  }
}

// Layout Manager Styles
.custom-layout-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.custom-layout-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  .layout-btn {
    padding: 6px 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: transparent;
    color: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    &.active {
      background: #4CAF50;
      border-color: #4CAF50;
    }
  }

  .tile-count {
    margin-left: auto;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
  }
}

// Focus layout specific styles
.custom-focus-layout-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;

  .custom-focus-main {
    flex: 1;
    min-height: 0;
  }

  .custom-focus-carousel {
    height: 120px;
    overflow-x: auto;
    overflow-y: hidden;

    .custom-tiles-container {
      height: 100%;
      display: flex;
      gap: 4px;
      padding: 4px;

      .custom-participant-tile {
        flex-shrink: 0;
        width: 100px;
        height: 100px;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .custom-layout-controls {
    padding: 6px 8px;

    .layout-btn {
      padding: 4px 8px;
      font-size: 11px;
    }

    .tile-count {
      font-size: 11px;
    }
  }

  .custom-focus-layout-container {
    .custom-focus-carousel {
      height: 80px;

      .custom-tiles-container .custom-participant-tile {
        width: 70px;
        height: 70px;
      }
    }
  }
}
