# ParticipantTile Memory Optimization Guide

## Overview
This document explains the memory leak issues found in the original ParticipantTile component and how they were fixed in the optimized version.

## Memory Leak Issues in Original ParticipantTile

### 1. **Timeout Management Issues**
```javascript
// ❌ PROBLEMATIC CODE (Original)
React.useEffect(() => {
  const timeout = setTimeout(() => {
    setRemoteEmojiReactions((prev) => {
      const newMap = new Map(prev);
      newMap.delete(trackReference.participant.identity);
      return newMap;
    });
  }, 6000);
  return () => clearTimeout(timeout);
}, [trackReference.participant, remoteEmojiReactions]); // ⚠️ remoteEmojiReactions causes re-runs
```

**Problems:**
- `remoteEmojiReactions` in dependency array causes effect to re-run on every Map change
- Multiple timeouts can be created without proper cleanup
- Stale closures capture old values

### 2. **Map State Management Issues**
```javascript
// ❌ PROBLEMATIC CODE (VideoConference.js)
setRemoteEmojiReactions((prev) =>
  new Map(prev).set(participant.identity, DataReceivedEvent.HEART)
);
```

**Problems:**
- Creates new Map instance on every update
- No cleanup when participants disconnect
- Memory accumulates over time

### 3. **Missing Participant Cleanup**
- No cleanup of participant data when they disconnect
- Maps continue to hold references to disconnected participants
- Memory usage grows with each participant join/leave cycle

## Fixed Implementation

### 1. **Optimized Timeout Management**
```javascript
// ✅ FIXED CODE (CustomParticipantTile.js)
const useTimeout = (callback, delay, dependencies = []) => {
  const timeoutRef = useRef(null);
  const callbackRef = useRef(callback);

  useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    if (delay !== null) {
      timeoutRef.current = setTimeout(() => {
        callbackRef.current();
        timeoutRef.current = null;
      }, delay);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, dependencies);
};
```

**Benefits:**
- Proper timeout cleanup with useRef
- Prevents multiple timeouts
- Stable callback references

### 2. **Optimized Map Operations**
```javascript
// ✅ FIXED CODE (VideoConference.js)
const updateMapState = (setMapState, key, value) => {
  setMapState(prevMap => {
    const newMap = new Map(prevMap);
    newMap.set(key, value);
    return newMap;
  });
};

const deleteFromMapState = (setMapState, key) => {
  setMapState(prevMap => {
    const newMap = new Map(prevMap);
    newMap.delete(key);
    return newMap;
  });
};

// Usage:
updateMapState(setRemoteEmojiReactions, participant.identity, DataReceivedEvent.HEART);
```

**Benefits:**
- Centralized Map operations
- Consistent state updates
- Easier to debug and maintain

### 3. **Participant Cleanup on Disconnect**
```javascript
// ✅ FIXED CODE (VideoConference.js)
const onParticipantDisconnected = (participant) => {
  const participantId = participant.identity;
  
  // Clean up all participant data
  deleteFromMapState(setRemoteEmojiReactions, participantId);
  deleteFromMapState(setRemoteRaisedHands, participantId);
  deleteFromMapState(setParticipantBrightness, participantId);
  
  // ... other cleanup
};
```

**Benefits:**
- Automatic memory cleanup
- Prevents memory accumulation
- Consistent cleanup pattern

## How to Use the Optimized Component

### 1. **Switch to CustomParticipantTile**
In `VideoConference.js`, the import has been updated:

```javascript
// ❌ Old (memory leaks)
// import { ParticipantTile } from "./ParticipantTile";

// ✅ New (memory optimized)
import { CustomParticipantTile as ParticipantTile } from "./CustomParticipantTile";
```

### 2. **Use Memory-Optimized Hooks**
```javascript
import { useTimeout, useMapState } from "../hooks/useParticipantTileMemory";

// Instead of useState for Maps:
const [reactions, setReactions] = useMapState();
```

### 3. **Initialize Maps with Functions**
```javascript
// ✅ Better initialization
const [remoteEmojiReactions, setRemoteEmojiReactions] = useState(() => new Map());
const [remoteRaisedHands, setRemoteRaisedHands] = useState(() => new Map());
```

## Performance Improvements

### 1. **Memoization**
- Participant data is memoized to prevent unnecessary re-renders
- Video styles are computed only when dependencies change
- Color management is optimized with proper caching

### 2. **Optimized Dependencies**
```javascript
// ❌ Before: Entire Map in dependencies
useEffect(() => {
  // ...
}, [remoteEmojiReactions]);

// ✅ After: Specific value only
useEffect(() => {
  // ...
}, [remoteEmojiReactions.get(participantId)]);
```

### 3. **Context Optimization**
- Context providers are memoized
- Unnecessary context creation is avoided

## Testing for Memory Leaks

### 1. **Chrome DevTools Memory Tab**
1. Open Chrome DevTools → Memory tab
2. Take heap snapshot before participants join
3. Have participants join and leave multiple times
4. Take another heap snapshot
5. Compare heap sizes - should not grow significantly

### 2. **Performance Monitoring**
```javascript
// Add to component for monitoring
useEffect(() => {
  console.log('Participant maps sizes:', {
    reactions: remoteEmojiReactions.size,
    raisedHands: remoteRaisedHands.size,
    brightness: participantBrightness.size
  });
}, [remoteEmojiReactions.size, remoteRaisedHands.size, participantBrightness.size]);
```

### 3. **React DevTools Profiler**
- Monitor re-render frequency
- Check for unnecessary component updates
- Verify memoization is working

## Migration Checklist

- [x] Updated ParticipantTile import in VideoConference.js
- [x] Optimized Map state management
- [x] Added participant cleanup on disconnect
- [x] Created memory-optimized hooks
- [x] Added proper timeout management
- [x] Implemented memoization for expensive operations

## Best Practices Going Forward

1. **Always use useRef for timeouts/intervals**
2. **Clean up participant data on disconnect**
3. **Use lazy initialization for Maps: `useState(() => new Map())`**
4. **Memoize expensive computations**
5. **Avoid entire Maps in dependency arrays**
6. **Test with multiple participants joining/leaving**

## Files Modified

1. `CustomParticipantTile.js` - New memory-optimized component
2. `VideoConference.js` - Updated to use optimized component and Map operations
3. `useParticipantTileMemory.js` - Custom hooks for memory management
4. `ParticipantTile.js` - Fixed original component (backup)

The optimized implementation maintains all existing functionality while preventing memory leaks and improving performance.
