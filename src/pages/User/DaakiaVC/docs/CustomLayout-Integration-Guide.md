# Custom Participant Tile Layout Integration Guide

## Overview
This guide shows you how to replace the existing LiveKit layouts with your own fully customizable participant tile system that gives you complete control over:

- **Layout Types**: Grid, Focus, Carousel, Speaker, Mobile views
- **Tile Count**: Control exactly how many tiles to show
- **Styling**: Complete SCSS control over appearance
- **Responsive Design**: Automatic adaptation to different screen sizes
- **Custom Configurations**: Create your own layout presets

## Quick Integration

### 1. Replace Existing Layout in VideoConference.js

```javascript
// Replace the existing layout sections with:
import { CustomLayoutManager, useCustomLayout } from './CustomLayoutManager';

// In your VideoConference component:
const {
  viewMode,
  maxVisibleTiles,
  setViewMode,
  setMaxVisibleTiles,
} = useCustomLayout('grid');

// Replace the existing grid/focus layout rendering with:
<CustomLayoutManager
  tracks={tracks}
  focusTrack={focusTrack}
  viewMode={viewMode}
  maxVisibleTiles={maxVisibleTiles}
  showEmojiReaction={showEmojiReaction}
  setShowEmojiReaction={setShowEmojiReaction}
  showRaiseHand={showRaiseHand}
  remoteRaisedHands={remoteRaisedHands}
  remoteEmojiReactions={remoteEmojiReactions}
  setRemoteEmojiReactions={setRemoteEmojiReactions}
  isSelfVideoMirrored={isSelfVideoMirrored}
  setIsSelfVideoMirrored={setIsSelfVideoMirrored}
  brightness={brightness}
  participantBrightness={participantBrightness}
  participantColors={participantColors}
  setParticipantColors={setParticipantColors}
/>
```

### 2. Add Custom Styling

Import the custom SCSS:
```javascript
import '../styles/CustomParticipantTile.scss';
```

## Layout Configurations

### Available Layout Types

1. **Grid View** - Standard grid layout
   - Max tiles per row: 4
   - Max visible tiles: 16
   - Aspect ratio: 16/9
   - Shows metadata and avatars

2. **Focus View** - Main speaker + carousel
   - Main speaker in large view
   - Other participants in small carousel
   - Perfect for presentations

3. **Carousel View** - Horizontal scrolling tiles
   - Compact tiles in a row
   - No metadata shown
   - Great for many participants

4. **Speaker View** - Single large tile
   - One participant at a time
   - Full metadata display
   - Large avatar when no video

5. **Mobile View** - Optimized for mobile
   - Portrait aspect ratio
   - Larger touch targets
   - Simplified metadata

### Custom Configuration Example

```javascript
const customConfig = {
  maxTilesPerRow: 3,
  maxVisibleTiles: 9,
  tileAspectRatio: '16/9',
  showMetadata: true,
  showAvatar: true,
  avatarSize: 'medium',
  className: 'my-custom-layout'
};

<CustomLayoutManager
  customLayoutConfig={customConfig}
  // ... other props
/>
```

## SCSS Customization

### Basic Tile Styling
```scss
.custom-participant-tile {
  border-radius: 12px; // Custom border radius
  border: 3px solid #your-brand-color;
  
  &.is-speaking {
    border-color: #speaking-color;
    box-shadow: 0 0 20px rgba(your-color, 0.5);
  }
}
```

### Layout-Specific Styling
```scss
.custom-grid-layout {
  // Grid view specific styles
  aspect-ratio: 4/3; // Change aspect ratio
}

.custom-focus-layout {
  // Focus view specific styles
  max-width: 150px; // Smaller carousel tiles
}
```

### Responsive Customization
```scss
@media (max-width: 768px) {
  .custom-participant-tile {
    .custom-metadata-container {
      padding: 4px 6px; // Smaller padding on mobile
    }
  }
}
```

## Advanced Features

### 1. Dynamic Layout Switching
```javascript
const handleParticipantCountChange = (count) => {
  if (count === 1) setViewMode('speaker');
  else if (count <= 4) setViewMode('grid');
  else if (count <= 8) setViewMode('focus');
  else setViewMode('carousel');
};
```

### 2. Custom Tile Click Handling
```javascript
const handleTileClick = (trackRef, participant) => {
  // Pin participant
  if (layoutContext?.pin?.dispatch) {
    layoutContext.pin.dispatch({ 
      msg: 'set_pin', 
      trackReference: trackRef 
    });
  }
  
  // Switch to focus view
  setViewMode('focus');
};
```

### 3. Layout Presets
```javascript
const layoutPresets = {
  meeting: {
    viewMode: 'grid',
    maxVisibleTiles: 9,
    showMetadata: true
  },
  presentation: {
    viewMode: 'focus',
    maxVisibleTiles: 6,
    showMetadata: false
  },
  webinar: {
    viewMode: 'carousel',
    maxVisibleTiles: 20,
    showMetadata: false
  }
};
```

## Integration Steps

### Step 1: Update VideoConference.js Imports
```javascript
// Add these imports
import { CustomLayoutManager, useCustomLayout } from './CustomLayoutManager';
import '../styles/CustomParticipantTile.scss';
```

### Step 2: Replace Layout Logic
Find the sections with `GridLayout`, `CarouselLayout`, and `FocusLayoutContainer` and replace with:

```javascript
// Replace all layout rendering with:
<div className="custom-video-layout">
  <CustomLayoutManager
    tracks={tracks}
    focusTrack={focusTrack}
    viewMode={viewMode}
    maxVisibleTiles={maxVisibleTiles}
    // ... all your existing props
  />
</div>
```

### Step 3: Add Layout Controls (Optional)
```javascript
<div className="layout-controls">
  <button onClick={() => setViewMode('grid')}>Grid</button>
  <button onClick={() => setViewMode('focus')}>Focus</button>
  <button onClick={() => setViewMode('speaker')}>Speaker</button>
  <select 
    value={maxVisibleTiles} 
    onChange={(e) => setMaxVisibleTiles(parseInt(e.target.value))}
  >
    <option value={4}>4 tiles</option>
    <option value={9}>9 tiles</option>
    <option value={16}>16 tiles</option>
  </select>
</div>
```

## Benefits of Custom Layout

### 1. **Complete Control**
- Exact tile count control
- Custom aspect ratios
- Your own CSS/SCSS styling
- Responsive breakpoints

### 2. **Performance Optimized**
- Memory leak prevention
- Efficient re-rendering
- Proper cleanup on participant disconnect

### 3. **Flexible Layouts**
- Multiple view modes
- Dynamic switching
- Custom configurations
- Mobile optimization

### 4. **Easy Customization**
- SCSS variables
- CSS custom properties
- Modular components
- Extensible architecture

## Migration Checklist

- [ ] Import CustomLayoutManager and useCustomLayout
- [ ] Import CustomParticipantTile.scss
- [ ] Replace GridLayout with CustomLayoutManager
- [ ] Replace CarouselLayout with CustomLayoutManager
- [ ] Replace FocusLayoutContainer with CustomLayoutManager
- [ ] Add layout state management with useCustomLayout
- [ ] Test all view modes (grid, focus, carousel, speaker)
- [ ] Verify responsive behavior
- [ ] Test with different participant counts
- [ ] Customize SCSS to match your design

## Troubleshooting

### Common Issues

1. **Tiles not showing**: Check that tracks are being passed correctly
2. **Layout not responsive**: Verify SCSS media queries are included
3. **Memory leaks**: Ensure proper cleanup in useEffect hooks
4. **Styling conflicts**: Use specific CSS selectors for custom styles

### Debug Mode
```javascript
<CustomLayoutManager
  tracks={tracks}
  viewMode={viewMode}
  onLayoutChange={(mode, config) => {
    console.log('Layout changed:', mode, config);
  }}
  onTileClick={(trackRef, participant) => {
    console.log('Tile clicked:', participant.identity);
  }}
  // ... other props
/>
```

This custom layout system gives you complete control over your video conference interface while maintaining all the functionality of your existing ParticipantTile component.
