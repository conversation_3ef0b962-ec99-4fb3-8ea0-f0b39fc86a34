import { useRef, useEffect, useCallback, useMemo, useState } from "react";

/**
 * Custom hook for managing timeouts with proper cleanup to prevent memory leaks
 * @param {Function} callback - Function to execute after delay
 * @param {number|null} delay - Delay in milliseconds, null to disable
 * @param {Array} dependencies - Dependencies array for effect
 * @returns {Function} clearTimeout function
 */
export const useTimeout = (callback, delay, dependencies = []) => {
  const timeoutRef = useRef(null);
  const callbackRef = useRef(callback);

  // Update callback ref when callback changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  // Clear timeout on unmount or dependency change
  useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    if (delay !== null && delay !== undefined) {
      timeoutRef.current = setTimeout(() => {
        callbackRef.current();
        timeoutRef.current = null;
      }, delay);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, dependencies);

  const clearTimeoutManually = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  return clearTimeoutManually;
};

/**
 * Custom hook for managing intervals with proper cleanup
 * @param {Function} callback - Function to execute on interval
 * @param {number|null} delay - Interval delay in milliseconds, null to disable
 * @param {Array} dependencies - Dependencies array for effect
 * @returns {Function} clearInterval function
 */
export const useInterval = (callback, delay, dependencies = []) => {
  const intervalRef = useRef(null);
  const callbackRef = useRef(callback);

  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  useEffect(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    if (delay !== null && delay !== undefined) {
      intervalRef.current = setInterval(() => {
        callbackRef.current();
      }, delay);
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, dependencies);

  const clearIntervalManually = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  return clearIntervalManually;
};

/**
 * Custom hook for managing event listeners with automatic cleanup
 * @param {EventTarget} target - Event target (element, window, etc.)
 * @param {string} eventType - Event type (click, resize, etc.)
 * @param {Function} handler - Event handler function
 * @param {Object} options - Event listener options
 */
export const useEventListener = (target, eventType, handler, options = {}) => {
  const handlerRef = useRef(handler);

  useEffect(() => {
    handlerRef.current = handler;
  }, [handler]);

  useEffect(() => {
    if (!target) return;

    const eventHandler = (event) => handlerRef.current(event);
    target.addEventListener(eventType, eventHandler, options);

    return () => {
      target.removeEventListener(eventType, eventHandler, options);
    };
  }, [target, eventType, options.capture, options.passive, options.once]);
};

/**
 * Custom hook for managing Map state with optimized updates
 * @param {Map} initialMap - Initial Map value
 * @returns {[Map, Function, Function, Function]} [map, setMap, updateKey, deleteKey]
 */
export const useMapState = (initialMap = new Map()) => {
  const [map, setMap] = useState(initialMap);

  const updateKey = useCallback((key, value) => {
    setMap(prevMap => {
      const newMap = new Map(prevMap);
      newMap.set(key, value);
      return newMap;
    });
  }, []);

  const deleteKey = useCallback((key) => {
    setMap(prevMap => {
      const newMap = new Map(prevMap);
      newMap.delete(key);
      return newMap;
    });
  }, []);

  const clearMap = useCallback(() => {
    setMap(new Map());
  }, []);

  return [map, setMap, updateKey, deleteKey, clearMap];
};

/**
 * Custom hook for debouncing values to prevent excessive re-renders
 * @param {any} value - Value to debounce
 * @param {number} delay - Debounce delay in milliseconds
 * @returns {any} Debounced value
 */
export const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Custom hook for managing previous value
 * @param {any} value - Current value
 * @returns {any} Previous value
 */
export const usePrevious = (value) => {
  const ref = useRef();
  
  useEffect(() => {
    ref.current = value;
  });
  
  return ref.current;
};

/**
 * Custom hook for managing component mount state
 * @returns {Object} { isMounted, setMounted }
 */
export const useMountedState = () => {
  const mountedRef = useRef(false);
  const [, forceUpdate] = useState({});

  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
    };
  }, []);

  const isMounted = useCallback(() => mountedRef.current, []);
  
  const setMounted = useCallback(() => {
    if (mountedRef.current) {
      forceUpdate({});
    }
  }, []);

  return { isMounted, setMounted };
};

/**
 * Custom hook for safe async operations that checks if component is still mounted
 * @param {Function} asyncFunction - Async function to execute
 * @returns {Function} Safe async function
 */
export const useSafeAsync = (asyncFunction) => {
  const { isMounted } = useMountedState();

  return useCallback(async (...args) => {
    if (!isMounted()) return;
    
    try {
      const result = await asyncFunction(...args);
      if (isMounted()) {
        return result;
      }
    } catch (error) {
      if (isMounted()) {
        throw error;
      }
    }
  }, [asyncFunction, isMounted]);
};

/**
 * Custom hook for managing LiveKit track references with memory optimization
 * @param {Object} trackRef - Track reference object
 * @returns {Object} Optimized track reference data
 */
export const useOptimizedTrackRef = (trackRef) => {
  return useMemo(() => {
    if (!trackRef?.participant) return null;

    return {
      participant: trackRef.participant,
      participantId: trackRef.participant.identity,
      isLocal: trackRef.participant.isLocal,
      source: trackRef.source,
      publication: trackRef.publication,
      track: trackRef.publication?.track,
    };
  }, [
    trackRef?.participant?.identity,
    trackRef?.participant?.isLocal,
    trackRef?.source,
    trackRef?.publication?.track,
  ]);
};

/**
 * Custom hook for managing participant reactions with automatic cleanup
 * @param {string} participantId - Participant identifier
 * @param {Map} reactions - Reactions map
 * @param {Function} setReactions - Function to update reactions
 * @param {number} duration - Reaction duration in milliseconds
 */
export const useParticipantReaction = (participantId, reactions, setReactions, duration = 6000) => {
  const currentReaction = reactions.get(participantId);

  useTimeout(
    () => {
      if (participantId && setReactions) {
        setReactions((prev) => {
          const newMap = new Map(prev);
          newMap.delete(participantId);
          return newMap;
        });
      }
    },
    currentReaction ? duration : null,
    [participantId, currentReaction]
  );

  return currentReaction;
};
