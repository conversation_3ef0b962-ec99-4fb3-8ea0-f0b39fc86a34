import React, { useMemo, useState, useCallback } from 'react';
import { CustomizableParticipantTile, LAYOUT_CONFIGS } from './CustomizableParticipantTile';
import '../styles/CustomParticipantTile.scss';

// Layout manager for complete control over participant tiles
export function CustomLayoutManager({
  tracks = [],
  focusTrack = null,
  viewMode = 'grid', // 'grid', 'focus', 'carousel', 'speaker', 'mobile'
  maxVisibleTiles: maxVisibleTilesProp = 16,
  showMetadata: showMetadataProp = true,
  showConnectionQuality = true,
  showMuteIndicator = true,
  showParticipantName = true,
  enableHoverEffects = true,
  customLayoutConfig = null,
  onTileClick = null,
  onLayoutChange = null,
  // All the participant tile props
  showEmojiReaction,
  setShowEmojiReaction,
  showRaiseHand,
  remoteRaisedHands,
  remoteEmojiReactions,
  setRemoteEmojiReactions,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  brightness,
  participantBrightness,
  participantColors,
  setParticipantColors,
  ...props
}) {
  const [currentViewMode, setCurrentViewMode] = useState(viewMode);

  // Get layout configuration
  const layoutConfig = useMemo(() => {
    if (customLayoutConfig) return customLayoutConfig;
    
    switch (currentViewMode) {
      case 'focus':
        return LAYOUT_CONFIGS.FOCUS_VIEW;
      case 'carousel':
        return LAYOUT_CONFIGS.CAROUSEL_VIEW;
      case 'speaker':
        return LAYOUT_CONFIGS.SPEAKER_VIEW;
      case 'mobile':
        return LAYOUT_CONFIGS.MOBILE_VIEW;
      default:
        return LAYOUT_CONFIGS.GRID_VIEW;
    }
  }, [currentViewMode, customLayoutConfig]);

  // Filter and limit tracks based on configuration
  const visibleTracks = useMemo(() => {
    let filteredTracks = [...tracks];

    // If there's a focus track, handle it separately
    if (focusTrack && currentViewMode === 'focus') {
      // Remove focus track from regular tracks
      filteredTracks = tracks.filter(track => track !== focusTrack);
      // Limit carousel tracks
      filteredTracks = filteredTracks.slice(0, maxVisibleTilesProp - 1);
    } else {
      // Limit total tracks
      filteredTracks = filteredTracks.slice(0, maxVisibleTilesProp);
    }

    return filteredTracks;
  }, [tracks, focusTrack, currentViewMode, maxVisibleTilesProp]);

  // Calculate container class based on tile count
  const containerClassName = useMemo(() => {
    const totalTiles = visibleTracks.length + (focusTrack && currentViewMode === 'focus' ? 1 : 0);
    
    let className = 'custom-tiles-container';
    
    if (totalTiles <= 16) {
      className += ` tiles-${totalTiles}`;
    } else {
      className += ' tiles-many';
    }
    
    className += ` view-mode-${currentViewMode}`;
    
    return className;
  }, [visibleTracks.length, focusTrack, currentViewMode]);

  // Handle tile click
  const handleTileClick = useCallback((trackRef, participant) => {
    if (onTileClick) {
      onTileClick(trackRef, participant);
    }
  }, [onTileClick]);

  // Handle view mode change
  const handleViewModeChange = useCallback((newViewMode) => {
    setCurrentViewMode(newViewMode);
    if (onLayoutChange) {
      onLayoutChange(newViewMode, LAYOUT_CONFIGS[newViewMode.toUpperCase() + '_VIEW']);
    }
  }, [onLayoutChange]);

  // Render focus layout (focus track + carousel)
  if (currentViewMode === 'focus' && focusTrack) {
    return (
      <div className="custom-focus-layout-container">
        {/* Main focus track */}
        <div className="custom-focus-main">
          <CustomizableParticipantTile
            trackRef={focusTrack}
            layoutConfig={LAYOUT_CONFIGS.SPEAKER_VIEW}
            viewMode="speaker"
            focusTrack
            showEmojiReaction={showEmojiReaction}
            setShowEmojiReaction={setShowEmojiReaction}
            showRaiseHand={showRaiseHand}
            remoteRaisedHands={remoteRaisedHands}
            remoteEmojiReactions={remoteEmojiReactions}
            setRemoteEmojiReactions={setRemoteEmojiReactions}
            isSelfVideoMirrored={isSelfVideoMirrored}
            setIsSelfVideoMirrored={setIsSelfVideoMirrored}
            brightness={brightness}
            participantBrightness={participantBrightness}
            participantColors={participantColors}
            setParticipantColors={setParticipantColors}
            showConnectionQuality={showConnectionQuality}
            showMuteIndicator={showMuteIndicator}
            showParticipantName={showParticipantName}
            enableHoverEffects={enableHoverEffects}
            onParticipantClick={(participant) => handleTileClick(focusTrack, participant)}
            tileIndex={0}
            totalTiles={visibleTracks.length + 1}
            {...props}
          />
        </div>
        
        {/* Carousel tracks */}
        {visibleTracks.length > 0 && (
          <div className="custom-focus-carousel">
            <div className={`custom-tiles-container tiles-${visibleTracks.length} view-mode-carousel`}>
              {visibleTracks.map((trackRef, index) => (
                <CustomizableParticipantTile
                  key={trackRef.participant?.identity || index}
                  trackRef={trackRef}
                  layoutConfig={LAYOUT_CONFIGS.CAROUSEL_VIEW}
                  viewMode="carousel"
                  showEmojiReaction={showEmojiReaction}
                  setShowEmojiReaction={setShowEmojiReaction}
                  showRaiseHand={showRaiseHand}
                  remoteRaisedHands={remoteRaisedHands}
                  remoteEmojiReactions={remoteEmojiReactions}
                  setRemoteEmojiReactions={setRemoteEmojiReactions}
                  isSelfVideoMirrored={isSelfVideoMirrored}
                  setIsSelfVideoMirrored={setIsSelfVideoMirrored}
                  brightness={brightness}
                  participantBrightness={participantBrightness}
                  participantColors={participantColors}
                  setParticipantColors={setParticipantColors}
                  showConnectionQuality={showConnectionQuality}
                  showMuteIndicator={showMuteIndicator}
                  showParticipantName={showParticipantName}
                  enableHoverEffects={enableHoverEffects}
                  onParticipantClick={(participant) => handleTileClick(trackRef, participant)}
                  tileIndex={index + 1}
                  totalTiles={visibleTracks.length + 1}
                  {...props}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  // Render regular grid/carousel/speaker layout
  return (
    <div className="custom-layout-container">
      {/* Layout controls */}
      <div className="custom-layout-controls">
        <button 
          className={`layout-btn ${currentViewMode === 'grid' ? 'active' : ''}`}
          onClick={() => handleViewModeChange('grid')}
        >
          Grid
        </button>
        <button 
          className={`layout-btn ${currentViewMode === 'speaker' ? 'active' : ''}`}
          onClick={() => handleViewModeChange('speaker')}
        >
          Speaker
        </button>
        <button 
          className={`layout-btn ${currentViewMode === 'carousel' ? 'active' : ''}`}
          onClick={() => handleViewModeChange('carousel')}
        >
          Carousel
        </button>
        <span className="tile-count">
          {`${visibleTracks.length} / ${tracks.length} participants`}
        </span>
      </div>

      {/* Tiles container */}
      <div className={containerClassName}>
        {visibleTracks.map((trackRef, index) => (
          <CustomizableParticipantTile
            key={trackRef.participant?.identity || index}
            trackRef={trackRef}
            layoutConfig={layoutConfig}
            viewMode={currentViewMode}
            showEmojiReaction={showEmojiReaction}
            setShowEmojiReaction={setShowEmojiReaction}
            showRaiseHand={showRaiseHand}
            remoteRaisedHands={remoteRaisedHands}
            remoteEmojiReactions={remoteEmojiReactions}
            setRemoteEmojiReactions={setRemoteEmojiReactions}
            isSelfVideoMirrored={isSelfVideoMirrored}
            setIsSelfVideoMirrored={setIsSelfVideoMirrored}
            brightness={brightness}
            participantBrightness={participantBrightness}
            participantColors={participantColors}
            setParticipantColors={setParticipantColors}
            showConnectionQuality={showConnectionQuality}
            showMuteIndicator={showMuteIndicator}
            showParticipantName={showParticipantName}
            enableHoverEffects={enableHoverEffects}
            onParticipantClick={(participant) => handleTileClick(trackRef, participant)}
            tileIndex={index}
            totalTiles={visibleTracks.length}
            {...props}
          />
        ))}
      </div>
    </div>
  );
}

// Hook for managing layout state
export const useCustomLayout = (initialViewMode = 'grid') => {
  const [viewMode, setViewMode] = useState(initialViewMode);
  const [maxVisibleTiles, setMaxVisibleTiles] = useState(16);
  const [customConfig, setCustomConfig] = useState(null);

  const updateLayout = useCallback((newViewMode, config = null) => {
    setViewMode(newViewMode);
    if (config) {
      setCustomConfig(config);
      setMaxVisibleTiles(config.maxVisibleTiles);
    }
  }, []);

  const createCustomConfig = useCallback((overrides) => {
    const baseConfig = LAYOUT_CONFIGS[viewMode.toUpperCase() + '_VIEW'] || LAYOUT_CONFIGS.GRID_VIEW;
    return {
      ...baseConfig,
      ...overrides
    };
  }, [viewMode]);

  return {
    viewMode,
    maxVisibleTiles,
    customConfig,
    setViewMode,
    setMaxVisibleTiles,
    setCustomConfig,
    updateLayout,
    createCustomConfig,
  };
};

// Utility function to calculate optimal layout
export const calculateOptimalLayout = (participantCount) => {
  if (participantCount === 1) return 'speaker';
  if (participantCount <= 4) return 'grid';
  if (participantCount <= 8) return 'focus';
  return 'carousel';
};

// Export layout configurations for external use
export { LAYOUT_CONFIGS };
