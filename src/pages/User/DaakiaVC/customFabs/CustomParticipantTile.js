import React, { useRef, useEffect, useState, useCallback, useMemo } from "react";
import { Track } from "livekit-client";
import { Avatar } from "antd";
import "../styles/ParticipantTile.scss";
import {
  isTrackReference,
  isTrackReferencePinned,
} from "@livekit/components-core";
import {
  ParticipantContext,
  TrackRefContext,
  useEnsureTrackRef,
  useFeatureContext,
  useMaybeLayoutContext,
  useMaybeParticipantContext,
  useMaybeTrackRefContext,
  ParticipantName,
  TrackMutedIndicator,
  ConnectionQualityIndicator,
  FocusToggle,
  LockLockedIcon,
  ScreenShareIcon,
  VideoTrack,
  AudioTrack,
  useParticipantTile,
  useIsEncrypted,
} from "@livekit/components-react";
import { generateAvatar } from "../utils/helper";
import { darkColors } from "../utils/constants";
import { RaiseHandOverlay } from "../components/raisehand/RaiseHandOverlay";
import { ReactionsOverlay } from "../components/reactions/ReactionOverlay";

// Custom hook for managing timeouts with proper cleanup
const useTimeout = (callback, delay, dependencies = []) => {
  const timeoutRef = useRef(null);
  const callbackRef = useRef(callback);

  // Update callback ref when callback changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  // Clear timeout on unmount or dependency change
  useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    if (delay !== null) {
      timeoutRef.current = setTimeout(() => {
        callbackRef.current();
        timeoutRef.current = null;
      }, delay);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, dependencies);

  const clearTimeoutManually = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  return clearTimeoutManually;
};

// Custom hook for participant color management
const useParticipantColor = (participant, participantColors, setParticipantColors) => {
  const [color, setColor] = useState("#fd4563");

  useEffect(() => {
    if (!participant) return;

    const participantId = participant.identity;
    const existingColor = participantColors?.get(participantId);

    if (existingColor) {
      setColor(existingColor);
    } else {
      const randomNumber = Math.floor(Math.random() * 50);
      const newColor = darkColors[randomNumber];
      setColor(newColor);

      if (setParticipantColors) {
        setParticipantColors(prevColors => {
          const newColors = new Map(prevColors);
          newColors.set(participantId, newColor);
          return newColors;
        });
      }
    }
  }, [participant?.identity, participantColors, setParticipantColors]);

  return color;
};

// Context providers with proper memoization
const ParticipantContextIfNeeded = React.memo(({ participant, children }) => {
  const hasContext = !!useMaybeParticipantContext();
  return participant && !hasContext ? (
    <ParticipantContext.Provider value={participant}>
      {children}
    </ParticipantContext.Provider>
  ) : (
    <>{children}</>
  );
});

const TrackRefContextIfNeeded = React.memo(({ trackRef, children }) => {
  const hasContext = !!useMaybeTrackRefContext();
  return trackRef && !hasContext ? (
    <TrackRefContext.Provider value={trackRef}>
      {children}
    </TrackRefContext.Provider>
  ) : (
    <>{children}</>
  );
});

export const CustomParticipantTile = React.forwardRef(function CustomParticipantTile(
  {
    trackRef,
    showEmojiReaction,
    setShowEmojiReaction,
    showRaiseHand,
    remoteRaisedHands = new Map(),
    remoteEmojiReactions = new Map(),
    setRemoteEmojiReactions,
    children,
    onParticipantClick,
    disableSpeakingIndicator,
    isSelfVideoMirrored,
    setIsSelfVideoMirrored,
    brightness = 100,
    participantBrightness = new Map(),
    participantColors = new Map(),
    setParticipantColors,
    focusTrack = false,
    ...htmlProps
  },
  ref
) {
  const trackReference = useEnsureTrackRef(trackRef);
  const { elementProps } = useParticipantTile({
    htmlProps,
    disableSpeakingIndicator,
    onParticipantClick,
    trackRef: trackReference,
  });

  const isEncrypted = useIsEncrypted(trackReference.participant);
  const layoutContext = useMaybeLayoutContext();
  const autoManageSubscription = useFeatureContext()?.autoSubscription;

  // Memoized participant data
  const { participant } = trackReference;
  const participantId = participant?.identity;
  const isLocal = participant?.isLocal;

  // Avatar name with proper memoization
  const avatarName = useMemo(() => {
    if (!participant) return "YO";
    return participant.name
      ? generateAvatar(participant.name)
      : generateAvatar(participant.identity);
  }, [participant?.name, participant?.identity]);

  // Color management
  const randomColor = useParticipantColor(participant, participantColors, setParticipantColors);

  // Subscription handler with proper cleanup
  const handleSubscribe = useCallback(
    (subscribed) => {
      if (
        trackReference.source &&
        !subscribed &&
        layoutContext?.pin?.dispatch &&
        isTrackReferencePinned(trackReference, layoutContext.pin.state)
      ) {
        layoutContext.pin.dispatch({ msg: "clear_pin" });
      }
    },
    [trackReference, layoutContext]
  );

  // Emoji reaction cleanup with custom hook
  const currentReaction = remoteEmojiReactions.get(participantId);
  useTimeout(
    () => {
      if (participantId && setRemoteEmojiReactions) {
        setRemoteEmojiReactions((prev) => {
          const newMap = new Map(prev);
          newMap.delete(participantId);
          return newMap;
        });
      }
    },
    currentReaction ? 6000 : null,
    [participantId, currentReaction]
  );

  // Local emoji reaction with speaking state
  useTimeout(
    () => {
      setShowEmojiReaction?.(null);
      if (isLocal && participant) {
        participant.setIsSpeaking(false);
      }
    },
    showEmojiReaction && isLocal ? 6000 : null,
    [showEmojiReaction, isLocal]
  );

  // Speaking indicator for reactions and raise hand
  useTimeout(
    () => {
      if (isLocal && participant) {
        participant.setIsSpeaking(false);
      }
    },
    (showEmojiReaction || showRaiseHand) && isLocal ? 500 : null,
    [showEmojiReaction, showRaiseHand, isLocal]
  );

  // Set speaking state when reactions/raise hand are active
  useEffect(() => {
    if (isLocal && participant && (showEmojiReaction || showRaiseHand)) {
      participant.setIsSpeaking(true);
    }
  }, [isLocal, participant, showEmojiReaction, showRaiseHand]);

  // Video style with memoization
  const videoStyle = useMemo(() => {
    const style = {};

    // Transform for camera
    if (
      trackReference.source === Track.Source.Camera &&
      trackReference.publication?.track?.mediaStreamTrack?.getSettings()
        .facingMode !== "environment"
    ) {
      style.transform = "rotateY(0deg)";
    }

    // Filters
    const filters = [];

    // Brightness for camera tracks
    if (trackReference.source === Track.Source.Camera) {
      const effectiveBrightness = isLocal
        ? brightness
        : (participantBrightness.get(participantId) || 100);
      filters.push(`brightness(${effectiveBrightness}%)`);
    }

    // Blur for local screen share
    if (trackReference.source === Track.Source.ScreenShare && isLocal) {
      filters.push("blur(70px)");
    }

    if (filters.length > 0) {
      style.filter = filters.join(" ");
    }

    return style;
  }, [
    trackReference.source,
    trackReference.publication?.track?.mediaStreamTrack,
    isLocal,
    brightness,
    participantBrightness,
    participantId
  ]);

  // Video class name with memoization
  const videoClassName = useMemo(() => {
    if (trackReference.source === Track.Source.Camera && isLocal) {
      return isSelfVideoMirrored ? "mirrored-video" : "not-mirrored-video";
    }
    return "";
  }, [trackReference.source, isLocal, isSelfVideoMirrored]);

  return (
    <div ref={ref} style={{ position: "relative" }} {...elementProps}>
      <TrackRefContextIfNeeded trackRef={trackReference}>
        <ParticipantContextIfNeeded participant={participant}>
          {children ?? (
            <>
              {/* Video/Audio Track Rendering */}
              {isTrackReference(trackReference) &&
              (trackReference.publication?.kind === "video" ||
                trackReference.source === Track.Source.Camera ||
                trackReference.source === Track.Source.ScreenShare) ? (
                <VideoTrack
                  trackRef={trackReference}
                  onSubscriptionStatusChanged={handleSubscribe}
                  manageSubscription={autoManageSubscription}
                  style={videoStyle}
                  className={videoClassName}
                />
              ) : (
                isTrackReference(trackReference) && (
                  <AudioTrack
                    trackRef={trackReference}
                    onSubscriptionStatusChanged={handleSubscribe}
                  />
                )
              )}

              {/* Screen Share Overlay */}
              {trackReference.source === Track.Source.ScreenShare && isLocal && (
                <div className="lk-screen-share-overlay">
                  <span>You are sharing your screen.</span>
                </div>
              )}

              {/* Overlays */}
              {showEmojiReaction && isLocal && (
                <ReactionsOverlay reaction={showEmojiReaction} />
              )}
              {showRaiseHand && isLocal && <RaiseHandOverlay />}
              {remoteRaisedHands.get(participantId) && <RaiseHandOverlay />}
              {remoteEmojiReactions.get(participantId) && (
                <ReactionsOverlay
                  reaction={remoteEmojiReactions.get(participantId)}
                />
              )}

              {/* Avatar Placeholder */}
              <div className="lk-participant-placeholder">
                <Avatar
                  className={`avatar-style primary-font ${
                    focusTrack ? "focus-track-avatar" : ""
                  }`}
                  style={{ backgroundColor: randomColor }}
                >
                  {avatarName}
                </Avatar>
              </div>

              {/* Metadata */}
              <div
                className={`lk-participant-metadata ${
                  focusTrack ? "focus-track-metadata" : ""
                }`}
              >
                <div className="lk-participant-metadata-item">
                  {trackReference.source === Track.Source.Camera ? (
                    <>
                      {isEncrypted && (
                        <LockLockedIcon style={{ marginRight: "0.25rem" }} />
                      )}
                      <TrackMutedIndicator
                        trackRef={{
                          participant: trackReference.participant,
                          source: Track.Source.Microphone,
                        }}
                        show="muted"
                      />
                      <ParticipantName />
                    </>
                  ) : (
                    <>
                      <ScreenShareIcon style={{ marginRight: "0.25rem" }} />
                      <ParticipantName>&apos;s screen</ParticipantName>
                    </>
                  )}
                </div>
                <ConnectionQualityIndicator className="lk-participant-metadata-item" />
              </div>
            </>
          )}
          <FocusToggle trackRef={trackReference} />
        </ParticipantContextIfNeeded>
      </TrackRefContextIfNeeded>
    </div>
  );
});
