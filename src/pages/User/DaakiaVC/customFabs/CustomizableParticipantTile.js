import React, { useEffect, useState, useCallback, useMemo } from "react";
import { Track } from "livekit-client";
import { Avatar } from "antd";
import "../styles/CustomParticipantTile.scss";
import {
  isTrackReference,
  isTrackReferencePinned,
} from "@livekit/components-core";
import {
  ParticipantContext,
  TrackRefContext,
  useEnsureTrackRef,
  useFeatureContext,
  useMaybeLayoutContext,
  useMaybeParticipantContext,
  useMaybeTrackRefContext,
  ParticipantName,
  TrackMutedIndicator,
  ConnectionQualityIndicator,
  FocusToggle,
  LockLockedIcon,
  ScreenShareIcon,
  VideoTrack,
  AudioTrack,
  useParticipantTile,
  useIsEncrypted,
} from "@livekit/components-react";
import { generateAvatar } from "../utils/helper";
import { darkColors } from "../utils/constants";
import { RaiseHandOverlay } from "../components/raisehand/RaiseHandOverlay";
import { ReactionsOverlay } from "../components/reactions/ReactionOverlay";
import { useTimeout } from "../hooks/useParticipantTileMemory";

// Layout configuration for different views
export const LAYOUT_CONFIGS = {
  GRID_VIEW: {
    maxTilesPerRow: 4,
    maxVisibleTiles: 16,
    tileAspectRatio: '16/9',
    showMetadata: true,
    showAvatar: true,
    avatarSize: 'medium',
    className: 'custom-grid-layout'
  },
  FOCUS_VIEW: {
    maxTilesPerRow: 6,
    maxVisibleTiles: 6,
    tileAspectRatio: '4/3',
    showMetadata: true,
    showAvatar: true,
    avatarSize: 'small',
    className: 'custom-focus-layout'
  },
  CAROUSEL_VIEW: {
    maxTilesPerRow: 8,
    maxVisibleTiles: 8,
    tileAspectRatio: '1/1',
    showMetadata: false,
    showAvatar: true,
    avatarSize: 'small',
    className: 'custom-carousel-layout'
  },
  SPEAKER_VIEW: {
    maxTilesPerRow: 1,
    maxVisibleTiles: 1,
    tileAspectRatio: '16/9',
    showMetadata: true,
    showAvatar: true,
    avatarSize: 'large',
    className: 'custom-speaker-layout'
  },
  MOBILE_VIEW: {
    maxTilesPerRow: 2,
    maxVisibleTiles: 4,
    tileAspectRatio: '9/16',
    showMetadata: true,
    showAvatar: true,
    avatarSize: 'medium',
    className: 'custom-mobile-layout'
  }
};

// Custom hook for participant color management
const useParticipantColor = (participant, participantColors, setParticipantColors) => {
  const [color, setColor] = useState("#fd4563");

  useEffect(() => {
    if (!participant) return;

    const participantId = participant.identity;
    const existingColor = participantColors?.get(participantId);

    if (existingColor) {
      setColor(existingColor);
    } else {
      const randomNumber = Math.floor(Math.random() * 50);
      const newColor = darkColors[randomNumber];
      setColor(newColor);

      if (setParticipantColors) {
        setParticipantColors(prevColors => {
          const newColors = new Map(prevColors);
          newColors.set(participantId, newColor);
          return newColors;
        });
      }
    }
  }, [participant?.identity, participantColors, setParticipantColors]);

  return color;
};

// Context providers with proper memoization
const ParticipantContextIfNeeded = React.memo(({ participant, children }) => {
  const hasContext = !!useMaybeParticipantContext();
  return participant && !hasContext ? (
    <ParticipantContext.Provider value={participant}>
      {children}
    </ParticipantContext.Provider>
  ) : (
    <>{children}</>
  );
});

const TrackRefContextIfNeeded = React.memo(({ trackRef, children }) => {
  const hasContext = !!useMaybeTrackRefContext();
  return trackRef && !hasContext ? (
    <TrackRefContext.Provider value={trackRef}>
      {children}
    </TrackRefContext.Provider>
  ) : (
    <>{children}</>
  );
});

export const CustomizableParticipantTile = React.forwardRef(function CustomizableParticipantTile(
  {
    trackRef,
    showEmojiReaction,
    setShowEmojiReaction,
    showRaiseHand,
    remoteRaisedHands = new Map(),
    remoteEmojiReactions = new Map(),
    setRemoteEmojiReactions,
    children,
    onParticipantClick,
    disableSpeakingIndicator,
    isSelfVideoMirrored,
    setIsSelfVideoMirrored,
    brightness = 100,
    participantBrightness = new Map(),
    participantColors = new Map(),
    setParticipantColors,
    focusTrack = false,
    // Layout customization props
    layoutConfig = LAYOUT_CONFIGS.GRID_VIEW,
    customClassName = '',
    showConnectionQuality = true,
    showMuteIndicator = true,
    showParticipantName = true,
    enableHoverEffects = true,
    tileIndex = 0,
    totalTiles = 1,
    viewMode = 'grid',
    ...htmlProps
  },
  ref
) {
  const trackReference = useEnsureTrackRef(trackRef);
  const { elementProps } = useParticipantTile({
    htmlProps,
    disableSpeakingIndicator,
    onParticipantClick,
    trackRef: trackReference,
  });

  const isEncrypted = useIsEncrypted(trackReference.participant);
  const layoutContext = useMaybeLayoutContext();
  const autoManageSubscription = useFeatureContext()?.autoSubscription;

  // Memoized participant data
  const { participant } = trackReference;
  const participantId = participant?.identity;
  const isLocal = participant?.isLocal;

  // Avatar name with proper memoization
  const avatarName = useMemo(() => {
    if (!participant) return "YO";
    return participant.name
      ? generateAvatar(participant.name)
      : generateAvatar(participant.identity);
  }, [participant?.name, participant?.identity]);

  // Color management
  const randomColor = useParticipantColor(participant, participantColors, setParticipantColors);

  // Subscription handler with proper cleanup
  const handleSubscribe = useCallback(
    (subscribed) => {
      if (
        trackReference.source &&
        !subscribed &&
        layoutContext?.pin?.dispatch &&
        isTrackReferencePinned(trackReference, layoutContext.pin.state)
      ) {
        layoutContext.pin.dispatch({ msg: "clear_pin" });
      }
    },
    [trackReference, layoutContext]
  );

  // Emoji reaction cleanup with custom hook
  const currentReaction = remoteEmojiReactions.get(participantId);
  useTimeout(
    () => {
      if (participantId && setRemoteEmojiReactions) {
        setRemoteEmojiReactions((prev) => {
          const newMap = new Map(prev);
          newMap.delete(participantId);
          return newMap;
        });
      }
    },
    currentReaction ? 6000 : null,
    [participantId, currentReaction]
  );

  // Local emoji reaction with speaking state
  useTimeout(
    () => {
      setShowEmojiReaction?.(null);
      if (isLocal && participant) {
        participant.setIsSpeaking(false);
      }
    },
    showEmojiReaction && isLocal ? 6000 : null,
    [showEmojiReaction, isLocal]
  );

  // Speaking indicator for reactions and raise hand
  useTimeout(
    () => {
      if (isLocal && participant) {
        participant.setIsSpeaking(false);
      }
    },
    (showEmojiReaction || showRaiseHand) && isLocal ? 500 : null,
    [showEmojiReaction, showRaiseHand, isLocal]
  );

  // Set speaking state when reactions/raise hand are active
  useEffect(() => {
    if (isLocal && participant && (showEmojiReaction || showRaiseHand)) {
      participant.setIsSpeaking(true);
    }
  }, [isLocal, participant, showEmojiReaction, showRaiseHand]);

  // Dynamic tile styling based on layout config
  const tileStyle = useMemo(() => {
    const style = {
      aspectRatio: layoutConfig.tileAspectRatio,
      '--tile-index': tileIndex,
      '--total-tiles': totalTiles,
    };

    // Add custom CSS variables for dynamic styling
    if (layoutConfig.maxTilesPerRow) {
      style['--max-tiles-per-row'] = layoutConfig.maxTilesPerRow;
    }

    return style;
  }, [layoutConfig, tileIndex, totalTiles]);

  // Video style with memoization
  const videoStyle = useMemo(() => {
    const style = {};

    // Transform for camera
    if (
      trackReference.source === Track.Source.Camera &&
      trackReference.publication?.track?.mediaStreamTrack?.getSettings()
        .facingMode !== "environment"
    ) {
      style.transform = isSelfVideoMirrored && isLocal ? "rotateY(180deg)" : "rotateY(0deg)";
    }

    // Filters
    const filters = [];

    // Brightness for camera tracks
    if (trackReference.source === Track.Source.Camera) {
      const effectiveBrightness = isLocal
        ? brightness
        : (participantBrightness.get(participantId) || 100);
      filters.push(`brightness(${effectiveBrightness}%)`);
    }

    // Blur for local screen share
    if (trackReference.source === Track.Source.ScreenShare && isLocal) {
      filters.push("blur(70px)");
    }

    if (filters.length > 0) {
      style.filter = filters.join(" ");
    }

    return style;
  }, [
    trackReference.source,
    trackReference.publication?.track?.mediaStreamTrack,
    isLocal,
    isSelfVideoMirrored,
    brightness,
    participantBrightness,
    participantId
  ]);

  // Combined class names
  const tileClassName = useMemo(() => {
    const classes = [
      'custom-participant-tile',
      layoutConfig.className,
      customClassName,
      viewMode && `view-mode-${viewMode}`,
      isLocal && 'is-local',
      focusTrack && 'is-focus-track',
      enableHoverEffects && 'hover-effects-enabled',
      trackReference.source === Track.Source.ScreenShare && 'is-screen-share',
      participant?.isSpeaking && 'is-speaking',
    ].filter(Boolean);

    return classes.join(' ');
  }, [
    layoutConfig.className,
    customClassName,
    viewMode,
    isLocal,
    focusTrack,
    enableHoverEffects,
    trackReference.source,
    participant?.isSpeaking
  ]);

  return (
    <div 
      ref={ref} 
      className={tileClassName}
      style={{ ...tileStyle, position: "relative" }} 
      {...elementProps}
    >
      <TrackRefContextIfNeeded trackRef={trackReference}>
        <ParticipantContextIfNeeded participant={participant}>
          {children ?? (
            <>
              {/* Video/Audio Track Rendering */}
              <div className="custom-media-container">
                {isTrackReference(trackReference) &&
                (trackReference.publication?.kind === "video" ||
                  trackReference.source === Track.Source.Camera ||
                  trackReference.source === Track.Source.ScreenShare) ? (
                  <VideoTrack
                    trackRef={trackReference}
                    onSubscriptionStatusChanged={handleSubscribe}
                    manageSubscription={autoManageSubscription}
                    style={videoStyle}
                    className="custom-video-track"
                  />
                ) : (
                  isTrackReference(trackReference) && (
                    <AudioTrack
                      trackRef={trackReference}
                      onSubscriptionStatusChanged={handleSubscribe}
                    />
                  )
                )}
              </div>

              {/* Screen Share Overlay */}
              {trackReference.source === Track.Source.ScreenShare && isLocal && (
                <div className="custom-screen-share-overlay">
                  <span>You are sharing your screen</span>
                </div>
              )}

              {/* Reaction Overlays */}
              {showEmojiReaction && isLocal && (
                <div className="custom-reaction-overlay local">
                  <ReactionsOverlay reaction={showEmojiReaction} />
                </div>
              )}
              
              {showRaiseHand && isLocal && (
                <div className="custom-raise-hand-overlay local">
                  <RaiseHandOverlay />
                </div>
              )}
              
              {remoteRaisedHands.get(participantId) && (
                <div className="custom-raise-hand-overlay remote">
                  <RaiseHandOverlay />
                </div>
              )}
              
              {remoteEmojiReactions.get(participantId) && (
                <div className="custom-reaction-overlay remote">
                  <ReactionsOverlay
                    reaction={remoteEmojiReactions.get(participantId)}
                  />
                </div>
              )}

              {/* Avatar Placeholder */}
              {layoutConfig.showAvatar && (
                <div className={`custom-avatar-container size-${layoutConfig.avatarSize}`}>
                  <Avatar
                    className="custom-avatar"
                    style={{ backgroundColor: randomColor }}
                    size={
                      layoutConfig.avatarSize === 'large' ? 80 :
                      layoutConfig.avatarSize === 'medium' ? 60 :
                      layoutConfig.avatarSize === 'small' ? 40 : 60
                    }
                  >
                    {avatarName}
                  </Avatar>
                </div>
              )}

              {/* Metadata */}
              {layoutConfig.showMetadata && (
                <div className="custom-metadata-container">
                  <div className="custom-metadata-content">
                    {trackReference.source === Track.Source.Camera ? (
                      <>
                        {isEncrypted && (
                          <LockLockedIcon className="custom-encryption-icon" />
                        )}
                        {showMuteIndicator && (
                          <TrackMutedIndicator
                            trackRef={{
                              participant: trackReference.participant,
                              source: Track.Source.Microphone,
                            }}
                            show="muted"
                            className="custom-mute-indicator"
                          />
                        )}
                        {showParticipantName && (
                          <ParticipantName className="custom-participant-name" />
                        )}
                      </>
                    ) : (
                      <>
                        <ScreenShareIcon className="custom-screen-share-icon" />
                        {showParticipantName && (
                          <ParticipantName className="custom-participant-name">
                            &apos;s screen
                          </ParticipantName>
                        )}
                      </>
                    )}
                  </div>
                  {showConnectionQuality && (
                    <ConnectionQualityIndicator className="custom-connection-quality" />
                  )}
                </div>
              )}

              {/* Focus Toggle */}
              <div className="custom-focus-toggle">
                <FocusToggle trackRef={trackReference} />
              </div>
            </>
          )}
        </ParticipantContextIfNeeded>
      </TrackRefContextIfNeeded>
    </div>
  );
});
